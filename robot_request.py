import requests
import json
import time
import socket

import hashlib
import base64
import hmac

from log_config import logger

# 机器人信息

WEBHOOK = "https://open.feishu.cn/open-apis/bot/v2/hook/91c4b3ab-f72d-4bc7-b75c-80905e6c3462"
SECRET = "3UUXoH6A682a6IrlciS4Ae"


def gen_sign(timestamp, secret):
    # 拼接timestamp和secret
    string_to_sign = "{}\n{}".format(timestamp, secret)
    hmac_code = hmac.new(string_to_sign.encode("utf-8"), digestmod=hashlib.sha256).digest()

    # 对结果进行base64处理
    sign = base64.b64encode(hmac_code).decode("utf-8")

    return sign


class RebotRequest:

    def __init__(self):
        self.url = WEBHOOK
        self.headers = {"Content-Type": "application/json"}
        self.payload = json.dumps({})

    def at_user(self, title, paragraph, user_ids):
        timestamp_int = int(time.time())
        sign_secret = SECRET
        coded_sign = gen_sign(timestamp_int, sign_secret)

        content_1 = [
            {
                "tag": "text",
                "text": paragraph + "\n",
            },
        ]
        for user_id in user_ids:
            content_1.append({
                "tag": "at",
                "user_id": user_id,
            })

        self.payload = json.dumps(
            {
                "timestamp": timestamp_int,
                "sign": coded_sign,
                "msg_type": "post",
                "content": {
                    "post": {
                        "zh_cn": {
                            "title": title,
                            "content": [content_1]
                        }
                    }
                }
            }
        )

    def set_payload(self, title, paragraph):
        timestamp_int = int(time.time())
        sign_secret = SECRET
        coded_sign = gen_sign(timestamp_int, sign_secret)

        self.payload = json.dumps(
            {
                "timestamp": timestamp_int,
                "sign": coded_sign,
                "msg_type": "post",
                "content": {
                    "post": {
                        "zh_cn": {
                            "title": title,
                            "content": [
                                [
                                    {
                                        "tag": "text",
                                        "text": paragraph,
                                    }
                                ],
                            ]
                        }
                    }
                }
            }
        )

    def request_feishu_robot(self):
        try:
            response = requests.request("POST", self.url, headers=self.headers, data=self.payload)
        except socket.gaierror as e:
            # 处理 "Temporary failure in name resolution" 异常
            logger.warning(e)
            logger.warning(
                "Temporary failure in name resolution 是指在 DNS 解析过程中出现了临时错误，"
                "无法找到目标主机的 IP 地址。这可能是由于网络故障、DNS 服务器故障或配置错误等原因造成的。")
            return False
        except:
            logger.warning("请求飞书机器人时出现未知错误！")
            return False
        else:
            response_dict = response.json()

            if response_dict.get("code") == 0 and response_dict.get("msg") == "success":
                logger.debug(f"Robot request, ok")
                """
                {
                     "StatusCode": 0,               //冗余字段，用于兼容存量历史逻辑，不建议使用
                     "StatusMessage": "success",    //冗余字段，用于兼容存量历史逻辑，不建议使用
                     "code": 0,
                     "data": {},
                     "msg": "success"
                }
                """
                return True
            else:
                logger.error(f"Failed to send a message by feishu robot")
                logger.error(response_dict)
                return False

    def request(self):
        try_times = 3
        for i in range(try_times):
            if self.request_feishu_robot():
                break
            else:
                if i != try_times - 1:
                    logger.debug(f"等待 30 秒后重试...")
                    time.sleep(30)
