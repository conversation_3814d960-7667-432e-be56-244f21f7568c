import os
import sys
import logging
import time

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger(__name__)

# 方案1：print 后刷新
print("print 语句 - 开始重启")
sys.stdout.flush()

# 方案2：日志后刷新
logger.info("日志语句 - 开始重启")
for handler in logger.handlers:
    handler.flush()

time.sleep(2)
logger.info("即将重启程序...")
for handler in logger.handlers:
    handler.flush()

os.execv(sys.executable, [sys.executable] + sys.argv)