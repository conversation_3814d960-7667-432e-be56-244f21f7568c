#!/usr/bin/python3

import os
import sys
import time
import datetime
import subprocess

import robot_request
from log_config import logger
import config

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
PASSWORD = "_7NVg3Tta3*Rd!40g@gZ"

# 全局变量
robot = None

VERSIONS = {
    "P0228609": {
        "BM": "2022-04-20", "BU": "2022-06-01", "CW": "2022-07-01", "CZ": "2022-08-01",
        "DB": "2022-09-01", "DV": "2022-10-24", "EC": "2022-11-23", "EI": "2022-12-14",
        "EN": "2022-12-29", "ET": "2023-01-28", "EU": "2023-02-07", "FA": "2023-03-14",
        "FF": "2023-04-07", "FJ": "2023-05-08", "GT": "2023-07-18", "HE": "2023-08-13",
        "HV": "2023-09-14", "JE": "2023-10-13", "JY": "2023-11-22", "KB": "2023-11-30",
        "KE": "2023-12-20", "KN": "2024-01-10", "KU": "2024-01-29", "LE": "2024-04-01",
        "EH": "2023-12-07", "GB": "2023-06-19", "HL": "2023-08-30", "JN": "2023-10-24",
        "KM": "2024-01-09", "LU": "2024-06-17", "MQ": "2024-08-01", "NG": "2024-09-12",
        "PU": "2025-01-10", "QV": "2025-05-20", "FC": "2023-03-21",
    },
    "P0271673": {
        "AJ": "2022-01-01", "BJ": "2023-01-16", "BN": "2023-03-14", "BW": "2023-08-01",
        "CB": "2023-09-05", "CF": "2023-12-05", "CG": "2023-12-25", "CM": "2024-03-14",
    },
    "P0297044": {
        "BY": "2023-06-19", "DM": "2023-09-09", "EJ": "2023-10-26", "EW": "2023-11-30",
        "FV": "2024-01-19", "GB": "2024-02-26", "GQ": "2024-04-01", "GY": "2024-04-25",
        "HN": "2024-06-28", "HZ": "2024-07-25", "EX": "2023-06-30", "FA": "2023-07-30",
        "GB": "2024-02-25", "HG": "2024-06-07", "JF": "2024-08-07", "KU": "2024-11-06",
        "LU": "2025-03-20", "MM": "2025-05-25", 
    },
    "P0297047": {
        "AD": "2023-04-23", "AS": "2023-09-08", "BB": "2023-12-25", "BG": "2024-03-14",
        "BR": "2024-08-21",
    },
}

# 执行指令 并输出到stdout
def run_cmd_and_output(command: str, timeout=None):
    try:
        result = subprocess.run(command, stdout=sys.stdout, stderr=subprocess.STDOUT,
                                shell=True, text=True, timeout=timeout)
    except subprocess.TimeoutExpired as err:
        logger.error(err)
        return False

    logger.debug(result)
    if result.returncode == 0:
        return True
    else:
        return False


# 执行指令 并获取输出
def get_cmd_result(command: str, timeout=None):
    try:
        result = subprocess.run(command, stdout=subprocess.PIPE, stderr=subprocess.PIPE,
                                shell=True, timeout=timeout)
    except subprocess.TimeoutExpired as err:
        return False, str(err)

    stdout = stderr = ""
    if result.stdout:
        stdout = result.stdout.decode()
    if result.stderr:
        stderr = result.stderr.decode()

    if result.returncode == 0:
        return True, stdout
    else:
        return False, stdout + stderr


def run_at_mazu_soc_cmd(soc_ip=config.SOC_IP):
    return f"sshpass -p '{PASSWORD}' ssh -o StrictHostKeyChecking=no mazu@{soc_ip} "


def get_current_version():
    cmd = run_at_mazu_soc_cmd() + "version"
    expected, result = get_cmd_result(cmd, 300)
    if expected:
        cmd = run_at_mazu_soc_cmd() + "version | grep SW_PART_NUMBER"
        expected, result = get_cmd_result(cmd, 10)
        if expected:
            current_software_version = result.split()[-2] + " " + result.split()[-1]
            return current_software_version
        else:
            logger.warning(result)
    else:
        logger.warning(result)
        if "retry count left" in result \
                or "Sysfault interface connection invalid" in result \
                or "Try to reconnect to serve" in result:
            logger.warning("Need to reboot mazu.")
            reboot_cmd = run_at_mazu_soc_cmd("**********") + "mcu_cmd -m 1 -c aurixreset"
            run_cmd_and_output(reboot_cmd)
            time.sleep(5)
            reboot_cmd = run_at_mazu_soc_cmd("**********") + "mcu_cmd -m 2 -c aurixreset"
            run_cmd_and_output(reboot_cmd)

    logger.warning("获取当前 ADC 软件版本失败！")
    return None


def convert_date_string(date_string: str):
    # 拆分日期字符串，获取月、日、年
    month, day, year = date_string.split('/')

    # 将日期字符串转换为目标格式
    new_date_string = f"{year}-{month}-{day}"

    return new_date_string


def get_build_date():
    cmd = run_at_mazu_soc_cmd() + "version | grep BUILD_DATE"
    expected, result = get_cmd_result(cmd, 10)
    if expected:
        result_list = result.split()
        oldest_date_str = None
        for index in range(1, len(result_list), 3):
            tmp_date_str = result_list[index]
            logger.debug(tmp_date_str)
            if '/' in tmp_date_str:
                date_str = convert_date_string(tmp_date_str)
            else:
                date_str = tmp_date_str

            if oldest_date_str is None or oldest_date_str > date_str:
                oldest_date_str = date_str
        return oldest_date_str

    logger.warning("获取当前 ADC 软件构建日期失败！")
    return None


def software_version_to_build_date(software_version) -> str:
    software_pn = software_version.split()[0]
    software_revision = software_version.split()[-1]
    return VERSIONS.get(software_pn).get(software_revision)


def get_firmware_list_to_be_upgraded(current_software_version, current_build_date):
    global robot
    # 获取当前 ADC 软件版本
    directory = os.path.join(CURRENT_DIR, "adc_software")
    current_software_pn = current_software_version.split()[0]
    current_software_revision = current_software_version.split()[-1]
                   
    # 检查当前产品是否在支持列表中
    software_pn_set = set(VERSIONS.keys())
    if current_software_pn not in software_pn_set:
        logger.error(f"目前只支持软件为 {software_pn_set} 的 ADC 进行软件刷写！")
        return None
    '''
    path_list = []              # 存储需要升级的固件包路径
    revision_list = []          # 存储需要升级的版本号
    oldest_software_revision = "ZZ"    # 固件包中最小的版本号（字符串比较）
    latest_software_revision = "AA"    # 固件包中最大的版本号（字符串比较）
    latest_software_file_path = None   # 最新版本的文件路径
    '''
    path_list = []
    revision_list = []
    oldest_software_revision = "ZZ"
    latest_software_revision = "AA"
    latest_software_file_path = None
    for file in os.listdir(directory):
        file_path = os.path.join(directory, file)
        if current_software_pn in file and ".tar.gz" in file and os.path.isfile(file_path):
            software_version = file.split(".")[0]
            software_revision = file.split()[-1].split(".")[0]
            if current_software_revision == "ZZ":
                # 
                tmp_build_date = software_version_to_build_date(software_version)
                if tmp_build_date and tmp_build_date > current_build_date:
                    path_list.append(file_path)
                    revision_list.append(software_revision)
            else:
                if software_revision > current_software_revision:
                    path_list.append(file_path)
                    revision_list.append(software_revision)

            if oldest_software_revision > software_revision:
                oldest_software_revision = software_revision
            if latest_software_revision < software_revision:
                latest_software_revision = software_revision
                latest_software_file_path = file_path

    # 需要有 ADC 固件包 <= 当前 ADC 的版本，不然可能导致跳过某些版本，导致刷写失败
    if oldest_software_revision > current_software_revision:
        # 当 oldest_software_revision == ZZ 说明没有任何一个匹配的固件包
        if oldest_software_revision == "ZZ":
            para = f"当前 ADC 的软件版本为：{current_software_version}\n" \
                   f"请补充下载 {current_software_version} 至最新 {current_software_pn} 之间的 ADC 固件包。"
        else:
            para = f"当前 ADC 的软件版本为：{current_software_version}\n" \
                   f"目前只支持软件版本为 {current_software_pn} {oldest_software_revision} 及其以上的 ADC 进行软件刷写！\n" \
                   f"请补充下载 {current_software_version} 至 {current_software_pn} {oldest_software_revision} 之间的 ADC 固件包。"

        logger.warning(para)
        if not config.DEBUG:
            robot.at_user(f"{config.VEHICLE} 需下载ADC固件包", para, config.MAINTAINER)
            robot.request()
        return None

    if len(revision_list) == 0 and current_software_revision == "ZZ":
        path_list.append(latest_software_file_path)
        revision_list.append(latest_software_revision)

    if len(revision_list) > 0:
        revision_list.sort()
        para = "当前 ADC 的软件版本为：" + current_software_version + "，需要刷写的固件包有：" + "->".join(revision_list)
        logger.info(para)
        if not config.DEBUG:
            robot.set_payload(f"{config.VEHICLE} 开始升级ADC", para)
            robot.request()

    path_list.sort()
    return path_list


def clean_mnt_flash_dir():
    global robot
    logger.debug("Clean SOC1 /mnt/flash/ dir")
    delete_flash_cmd = f"echo '{PASSWORD}' | sudo -S rm -rf /mnt/flash/*"
    delete_flash_script = os.path.join(tmp_dir, "delete_flash_script")
    with open(delete_flash_script, 'w') as f:
        f.write(delete_flash_cmd)

    cmd = run_at_mazu_soc_cmd() + f"'bash -s' < {delete_flash_script}"
    expected, result = get_cmd_result(cmd, 30)
    if expected:
        return True
    elif "No such file or directory" in result or "不存在" in result:
        return True
    else:
        logger.error(result)

    para = "清除 SOC1 /mnt/flash/ 目录下文件失败！"
    logger.error(para)
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} 清除flash失败", para, config.MAINTAINER)
        robot.request()
    return False


def delete_certs():
    global robot
    logger.debug("Delete certs")
    delete_certs_cmd = f"echo '{PASSWORD}' | sudo -S rm -rf /nio/certs"
    delete_certs_script = os.path.join(tmp_dir, "delete_certs_script")
    with open(delete_certs_script, 'w') as f:
        f.write(delete_certs_cmd)

    cmd = run_at_mazu_soc_cmd() + f"'bash -s' < {delete_certs_script}"
    expected, result = get_cmd_result(cmd, 30)
    if expected:
        return True
    elif "No such file or directory" in result or "不存在" in result:
        return True
    else:
        logger.error(result)

    para = "删除 SOC1 /nio/certs 失败！"
    logger.error(para)
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} 删证书失败", para, config.MAINTAINER)
        robot.request()
    return False


def copy_firmware_to_s1(path: str):
    global robot
    logger.info(f"Copy {path} to mazu@{config.SOC_IP}:/mnt/flash/")
    cmd = f"sshpass -p '{PASSWORD}' scp '{path}' mazu@{config.SOC_IP}:/mnt/flash/"

    if run_cmd_and_output(cmd, 1800):
        return True

    para = f"传输 ADC 固件包 {path} 至 SOC1 /mnt/flash/ 失败！"
    logger.error(para)
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} 传固件包失败", para, config.MAINTAINER)
        robot.request()
    return False


def update_ssh_key(iterations=4, step=1):
    ip = config.SOC_IP
    parts = ip.split('.')
    base_ip = '.'.join(parts[:-1])  # 获取除最后一位以外的IP部分
    last_part = int(parts[-1])  # 获取最后一位IP部分并转换为整数

    for i in range(0, iterations, step):
        new_last_part = last_part + i
        new_ip = f"{base_ip}.{new_last_part}"
        logger.debug(f"更新 {new_ip} SSH key...")
        cmd = f"ssh-keygen -f {os.path.expanduser('~')}/.ssh/known_hosts -R {ip}"
        expected, result = get_cmd_result(cmd, 30)
        if not expected:
            if not (".ssh/known_hosts updated" in result
                    or "not found in .ssh/known_hosts" in result):
                logger.error(result)
                logger.error(f"更新 {ip} SSH key 失败！")
                return False

    return True


def wait_mazu_reboot():
    time.sleep(60)
    try_times = 3
    for i in range(try_times):
        logger.debug(f"第 {i + 1} 次尝试 ping SOC1")
        cmd = f"ping -c 2 {config.SOC_IP}"
        if run_cmd_and_output(cmd):
            logger.debug("MAZU 重启完成。")
            return True
        else:
            if i != try_times - 1:
                logger.debug(f"等待 30 秒后重试...")
                time.sleep(30)

    logger.error(f"MAZU 重启失败！3次重试后仍无法连接，程序将重启...")
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} MAZU重启失败", "MAZU 重启失败，3次重试后仍无法连接，程序将自动重启", config.MAINTAINER)
        robot.request()

    # 重启程序
    logger.info("正在重启程序...")
    os.execv(sys.executable, [sys.executable] + sys.argv)


def upgrade_adc(file_name: str):
    global robot
    upgrade_cmd = f"echo '{PASSWORD}' | sudo -S mazu-flash -b /mnt/flash/'{file_name}' -r"
    upgrade_script = os.path.join(tmp_dir, "upgrade_script")
    with open(upgrade_script, 'w') as f:
        f.write(upgrade_cmd)

    cmd = run_at_mazu_soc_cmd() + f"'bash -s' < {upgrade_script}"
    expected = run_cmd_and_output(cmd)

    msg = ""
    target_version = file_name.split(".")[0]
    if expected:
        # 等待 ADC 重启
        logger.info("等待 MAZU 重启 ...")
        if not wait_mazu_reboot():
            msg = "MAZU 重启失败!\n"
        else:
            # 更新 SSH key
            if not update_ssh_key():
                msg = "更新 SSH key 失败!\n"
            else:
                # 检查当前版本是否为要升级的版本
                try_times = 3
                current_software_version = None
                for i in range(try_times):
                    logger.debug(f"第 {i + 1} 次尝试获取当前 ADC 软件版本。")
                    current_software_version = get_current_version()
                    if current_software_version:
                        if current_software_version == target_version:
                            time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                            para = f"{time_str} ADC 已刷写至 {target_version}"
                            logger.info(para)
                            if not config.DEBUG:
                                robot.set_payload(f"{config.VEHICLE} 刷写至 {target_version}", para)
                                robot.request()
                            return True
                        else:
                            msg = f"当前 ADC 软件版本为 {current_software_version}，不是 {target_version}\n"
                            break
                    else:
                        if i != try_times - 1:
                            logger.debug(f"等待 30 秒后重试...")
                            time.sleep(30)

                if current_software_version is None:
                    msg = "获取当前 ADC 软件版本失败！\n"

    time_str = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    para = msg + f"{time_str} ADC 刷写 {target_version} 失败！"
    logger.error(para)
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} 刷写失败 {target_version}", para, config.MAINTAINER)
        robot.request()
    return False


if __name__ == "__main__":
    tmp_dir = os.path.join(CURRENT_DIR, "tmp")
    if not os.path.exists(tmp_dir):
        os.mkdir(tmp_dir)

    # 飞书机器人
    robot = robot_request.RebotRequest()

    # 先更新 SSH key
    if not update_ssh_key():
        exit()

    # 获取当前 ADC 的软件版本
    logger.debug(f"尝试获取当前 ADC 软件版本。")
    current_adc_sw_version = get_current_version()
    build_date = None
    if current_adc_sw_version:
        if "ZZ" not in current_adc_sw_version:
            logger.debug(f"当前 ADC 软件版本为：{current_adc_sw_version}")
        else:
            build_date = get_build_date()
            logger.debug(f"当前 ADC 软件版本为：{current_adc_sw_version}，构建日期为 {build_date}。")
    else:
        exit()

    # 获取需要刷写的固件列表
    firmware_list = get_firmware_list_to_be_upgraded(current_adc_sw_version, build_date)
    if firmware_list is None:
        exit()

    main_para = ""
    if len(firmware_list) == 0:
        # 当前 ADC 的版本 >=  已下载的所有 ADC 固件包，不再进行软件刷写
        main_para = f"当前 ADC 的软件版本为：{current_adc_sw_version}\n" \
                    f"已不低与已下载的所有 ADC 固件包，不再进行软件刷写。\n"
    else:
        delete_certs()

        # 循环刷写固件包
        start_time = time.time()
        for firmware_path in firmware_list:
            firmware_name = os.path.basename(firmware_path)
            if not (clean_mnt_flash_dir() and copy_firmware_to_s1(firmware_path)
                    and upgrade_adc(firmware_name)):
                exit()
        end_time = time.time()
        elapsed_time = end_time - start_time
        averaged_minutes = round(elapsed_time / 60 / len(firmware_list), 2)

        clean_mnt_flash_dir()
        delete_certs()

        main_para = f"升级完成，共刷写 {len(firmware_list)} 个版本，平均耗时 {averaged_minutes} 分钟\n"

    main_para += "请按照以下步骤更换 MAZU\n" \
                 "- 更换 MAZU 硬件\n" \
                 "- 用 BD2 关闭车载诊断防火墙\n" \
                 "- 用 BD2 进行冷却系统排气，并加注适量冷却液\n" \
                 "- 重新启动本程序\n" \
                 "- 中控->行车驻车->【离车不下电模式】ON\n"
    logger.info(main_para)
    if not config.DEBUG:
        robot.at_user(f"{config.VEHICLE} ADC升级完成", main_para, config.MAZU_OPERATOR)
        robot.request()
